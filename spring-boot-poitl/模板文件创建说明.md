# POI-TL 模板文件创建说明

## 概述

POI-TL使用Word模板文件(.docx)作为基础，通过在模板中定义占位符来实现动态内容替换。

## 模板文件位置

所有模板文件应放置在：`src/main/resources/templates/` 目录下

## 占位符语法

### 1. 基础文本占位符
```
{{变量名}}
```
示例：`{{title}}`, `{{author}}`, `{{content}}`

### 2. 表格占位符
```
{{@表格变量名}}
```
示例：`{{@simpleTable}}`, `{{@complexTable}}`

### 3. 列表占位符
```
{{*列表变量名}}
```
示例：`{{*unorderedList}}`, `{{*orderedList}}`

### 4. 图片占位符
```
{{%图片变量名}}
```
示例：`{{%localImage}}`, `{{%webImage}}`

## 需要创建的模板文件

### 1. basic_text_template.docx
**用途**: 基础文本示例
**占位符**:
- `{{title}}` - 文档标题
- `{{author}}` - 作者
- `{{content}}` - 内容
- `{{styledTitle}}` - 带样式的标题
- `{{redBoldText}}` - 红色粗体文本
- `{{blueItalicText}}` - 蓝色斜体文本
- `{{combinedText}}` - 组合样式文本

**创建步骤**:
1. 新建Word文档
2. 输入以下内容：
```
文档标题：{{styledTitle}}

作者：{{author}}
创建日期：{{createDate}}

正文内容：
{{content}}

样式示例：
{{redBoldText}}
{{blueItalicText}}
{{combinedText}}
```

### 2. simple_table_template.docx
**用途**: 简单表格示例
**占位符**:
- `{{@simpleTable}}` - 简单表格

**创建步骤**:
1. 新建Word文档
2. 输入标题："员工信息表"
3. 在下方插入占位符：`{{@simpleTable}}`

### 3. complex_table_template.docx
**用途**: 复杂表格示例
**占位符**:
- `{{@complexTable}}` - 复杂表格

**创建步骤**:
1. 新建Word文档
2. 输入标题："员工详细信息统计"
3. 在下方插入占位符：`{{@complexTable}}`

### 4. list_template.docx
**用途**: 列表示例
**占位符**:
- `{{*unorderedList}}` - 无序列表
- `{{*orderedList}}` - 有序列表
- `{{*letterList}}` - 字母编号列表

**创建步骤**:
1. 新建Word文档
2. 输入以下内容：
```
POI-TL功能列表

无序列表：
{{*unorderedList}}

有序列表（开发流程）：
{{*orderedList}}

字母编号列表（项目阶段）：
{{*letterList}}
```

### 5. image_template.docx
**用途**: 图片示例
**占位符**:
- `{{%localImage}}` - 本地图片
- `{{%webImage}}` - 网络图片
- `{{%base64Image}}` - Base64图片

**创建步骤**:
1. 新建Word文档
2. 输入以下内容：
```
图片示例文档

本地图片：
{{%localImage}}

网络图片：
{{%webImage}}

Base64图片：
{{%base64Image}}
```

### 6. hyperlink_template.docx
**用途**: 超链接示例
**占位符**:
- `{{officialLink}}` - 官网链接
- `{{poiLink}}` - POI文档链接
- `{{emailLink}}` - 邮箱链接

**创建步骤**:
1. 新建Word文档
2. 输入以下内容：
```
超链接示例

相关链接：
官方网站：{{officialLink}}
技术文档：{{poiLink}}
联系我们：{{emailLink}}
```

### 7. comprehensive_report_template.docx
**用途**: 综合报告示例
**占位符**:
- `{{reportTitle}}` - 报告标题
- `{{reportDate}}` - 报告日期
- `{{department}}` - 部门
- `{{reporter}}` - 报告人
- `{{summaryTitle}}` - 摘要标题
- `{{summaryContent}}` - 摘要内容
- `{{@salesTable}}` - 销售数据表格
- `{{*keyCustomers}}` - 重点客户列表
- `{{*nextMonthPlan}}` - 下月计划
- `{{importantNote}}` - 重要提醒

### 8. dynamic_table_template.docx
**用途**: 动态表格示例
**占位符**:
- `{{@dynamicTable}}` - 动态表格
- `{{totalEmployees}}` - 员工总数
- `{{avgSalary}}` - 平均薪资

## 模板创建技巧

### 1. 格式设置
- 可以在模板中预设字体、颜色、对齐方式等格式
- 占位符会继承所在位置的格式设置

### 2. 表格设计
- 表格占位符应单独占据一个段落
- 可以在模板中预设表格的基本样式

### 3. 图片处理
- 图片占位符建议单独成行
- 可以预设图片的对齐方式

### 4. 样式继承
- 占位符会继承模板中设置的样式
- 代码中的样式设置会覆盖模板样式

## 快速创建模板

### 方法1：手动创建
1. 使用Microsoft Word创建.docx文件
2. 按照上述说明插入占位符
3. 保存到templates目录

### 方法2：复制现有模板
1. 复制已有的template.docx
2. 修改内容和占位符
3. 重命名并保存

## 注意事项

1. **文件格式**: 必须使用.docx格式，不支持.doc
2. **占位符语法**: 严格按照`{{}}`格式，注意大小写
3. **文件编码**: 建议使用UTF-8编码
4. **路径设置**: 确保文件放在正确的templates目录下
5. **变量名称**: 占位符变量名要与代码中的变量名完全一致

## 测试模板

创建模板后，可以通过以下方式测试：

1. 启动Spring Boot应用
2. 访问对应的接口
3. 检查生成的Word文档是否正确
4. 如有问题，检查占位符是否正确设置

## 常见问题

### Q: 占位符没有被替换？
A: 检查变量名是否与代码中一致，注意大小写

### Q: 表格格式不正确？
A: 确保表格占位符单独成段，检查MergeCellRule设置

### Q: 图片无法显示？
A: 检查图片路径是否正确，网络图片需要确保网络可访问

### Q: 中文显示乱码？
A: 确保模板文件使用UTF-8编码保存
