package com.example.controller;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.*;
import com.deepoove.poi.data.style.BorderStyle;

import jakarta.annotation.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * Word文档生成控制器
 * <p>
 * 该控制器提供Word文档生成功能，使用POI-TL模板引擎
 * 基于预定义的Word模板生成包含用户信息表格的文档
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
public class WordController {

    // 用于加载classpath下的Word模板文件
    @Resource
    private ResourceLoader resourceLoader;

    @GetMapping("/generate-word")
    public ResponseEntity<byte[]> generate() throws IOException {

        // 创建合并单元格的表头 - 必须也是5列
        RowRenderData mergedHeader = Rows.of("基本信息", "", "", "联系方式", "").textColor("FFFFFF")
                .bgColor("4472C4").center().create();

        // 创建表头行 - 5列
        RowRenderData header = Rows.of("姓名", "年龄", "性别", "手机号", "邮箱").center().create();

        // 创建数据行 - 5列
        RowRenderData row1 = Rows.of("张三", "28", "男", "13800138000", "<EMAIL>").center().create();
        RowRenderData row2 = Rows.of("李四", "30", "女", "13900139000", "<EMAIL>").center().create();

        TableRenderData table = Tables.of(mergedHeader, header, row1, row2).border(BorderStyle.DEFAULT).create();

        MergeCellRule rule = MergeCellRule.builder()
                .map(MergeCellRule.Grid.of(0, 0), MergeCellRule.Grid.of(0, 2))
                .map(MergeCellRule.Grid.of(0, 3), MergeCellRule.Grid.of(0, 4)).build();
        table.setMergeRule(rule);


        // 创建表格
        Map<String, Object> data = new HashMap<>();
        data.put("table01", table);

        // 使用ResourceLoader加载模板
        InputStream templateStream = resourceLoader.getResource("classpath:templates/template.docx").getInputStream();
        XWPFTemplate template = XWPFTemplate.compile(templateStream).render(data);

        // 生成文档字节流
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        template.write(outputStream);
        template.close();
        templateStream.close();

        // 构建响应
        HttpHeaders headersResponse = new HttpHeaders();
        headersResponse.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headersResponse.setContentDispositionFormData("attachment", "user_info.docx");

        return ResponseEntity.ok()
                .headers(headersResponse)
                .body(outputStream.toByteArray());
    }

    /**
     * 基础文本示例 - 演示文本替换和样式设置
     * 基于POI官方文档：https://poi.apache.org/components/document/
     */
    @GetMapping("/basic-text")
    public ResponseEntity<byte[]> generateBasicText() throws IOException {

        // 1. 基础文本数据
        Map<String, Object> data = new HashMap<>();
        data.put("title", "POI-TL基础文本示例");
        data.put("author", "系统管理员");
        data.put("content", "这是基础文本内容，演示POI-TL的文本处理功能。");

        // 2. 带样式的文本 - 红色粗体
        TextRenderData redBoldText = Texts.of("重要提示：这是红色粗体文本")
                .color("FF0000")
                .bold()
                .fontSize(14)
                .create();
        data.put("redBoldText", redBoldText);

        // 3. 蓝色斜体文本
        TextRenderData blueItalicText = Texts.of("注意：这是蓝色斜体文本")
                .color("0000FF")
                .italic()
                .fontSize(12)
                .create();
        data.put("blueItalicText", blueItalicText);

        // 4. 组合样式文本
        TextRenderData combinedText = Texts.of("组合样式：粗体+斜体+绿色+大字体")
                .bold()
                .italic()
                .color("00AA00")
                .fontSize(16)
                .create();
        data.put("combinedText", combinedText);

        // 使用模板渲染
        InputStream templateStream = resourceLoader.getResource("classpath:templates/basic_text_template.docx").getInputStream();
        XWPFTemplate template = XWPFTemplate.compile(templateStream).render(data);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        template.write(outputStream);
        template.close();
        templateStream.close();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "basic_text_example.docx");

        return ResponseEntity.ok()
                .headers(headers)
                .body(outputStream.toByteArray());
    }

    /**
     * 简单表格示例 - 演示基础表格创建
     */
    @GetMapping("/simple-table")
    public ResponseEntity<byte[]> generateSimpleTable() throws IOException {

        // 创建表头
        RowRenderData header = Rows.of("序号", "姓名", "部门", "薪资")
                .textColor("FFFFFF")
                .bgColor("4472C4")
                .center()
                .bold()
                .create();

        // 创建数据行
        RowRenderData row1 = Rows.of("1", "张三", "技术部", "8000").center().create();
        RowRenderData row2 = Rows.of("2", "李四", "市场部", "7500").center().create();
        RowRenderData row3 = Rows.of("3", "王五", "人事部", "6500").center().create();

        // 创建表格
        TableRenderData table = Tables.of(header, row1, row2, row3)
                .border(BorderStyle.DEFAULT)
                .create();

        Map<String, Object> data = new HashMap<>();
        data.put("simpleTable", table);

        InputStream templateStream = resourceLoader.getResource("classpath:templates/simple_table_template.docx").getInputStream();
        XWPFTemplate template = XWPFTemplate.compile(templateStream).render(data);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        template.write(outputStream);
        template.close();
        templateStream.close();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "simple_table_example.docx");

        return ResponseEntity.ok()
                .headers(headers)
                .body(outputStream.toByteArray());
    }

    /**
     * 复杂表格示例 - 演示合并单元格和复杂样式
     */
    @GetMapping("/complex-table")
    public ResponseEntity<byte[]> generateComplexTable() throws IOException {

        // 第一行：大标题合并单元格
        RowRenderData titleRow = Rows.of("员工信息统计表", "", "", "", "")
                .textColor("FFFFFF")
                .bgColor("2F5597")
                .center()
                .bold()
                .fontSize(16)
                .create();

        // 第二行：分组标题
        RowRenderData groupHeader = Rows.of("基本信息", "", "工作信息", "", "联系方式")
                .textColor("FFFFFF")
                .bgColor("4472C4")
                .center()
                .bold()
                .create();

        // 第三行：具体字段标题
        RowRenderData fieldHeader = Rows.of("姓名", "年龄", "部门", "职位", "手机号")
                .textColor("000000")
                .bgColor("D9E2F3")
                .center()
                .bold()
                .create();

        // 数据行
        RowRenderData data1 = Rows.of("张三", "28", "技术部", "Java工程师", "13800138000").center().create();
        RowRenderData data2 = Rows.of("李四", "30", "市场部", "销售经理", "13900139000").center().create();
        RowRenderData data3 = Rows.of("王五", "25", "人事部", "HR专员", "13700137000").center().create();

        // 创建表格
        TableRenderData table = Tables.of(titleRow, groupHeader, fieldHeader, data1, data2, data3)
                .border(BorderStyle.DEFAULT)
                .create();

        // 设置合并规则
        MergeCellRule mergeRule = MergeCellRule.builder()
                .map(MergeCellRule.Grid.of(0, 0), MergeCellRule.Grid.of(0, 4))  // 标题行合并
                .map(MergeCellRule.Grid.of(1, 0), MergeCellRule.Grid.of(1, 1))  // "基本信息"合并
                .map(MergeCellRule.Grid.of(1, 2), MergeCellRule.Grid.of(1, 3))  // "工作信息"合并
                .build();
        table.setMergeRule(mergeRule);

        Map<String, Object> templateData = new HashMap<>();
        templateData.put("complexTable", table);

        InputStream templateStream = resourceLoader.getResource("classpath:templates/complex_table_template.docx").getInputStream();
        XWPFTemplate template = XWPFTemplate.compile(templateStream).render(templateData);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        template.write(outputStream);
        template.close();
        templateStream.close();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "complex_table_example.docx");

        return ResponseEntity.ok()
                .headers(headers)
                .body(outputStream.toByteArray());
    }

    /**
     * 列表示例 - 演示有序和无序列表
     */
    @GetMapping("/list-example")
    public ResponseEntity<byte[]> generateListExample() throws IOException {

        Map<String, Object> data = new HashMap<>();

        // 1. 无序列表
        NumberingRenderData unorderedList = Numberings.create(
                "POI-TL基础功能",
                "文本处理和格式设置",
                "表格创建和样式",
                "图片插入和调整",
                "列表和编号"
        );
        data.put("unorderedList", unorderedList);

        // 2. 有序列表（数字编号）
        NumberingRenderData orderedList = Numberings.ofDecimal(
                "准备Word模板文件",
                "定义数据模型",
                "编写控制器代码",
                "测试文档生成",
                "部署到生产环境"
        ).create();
        data.put("orderedList", orderedList);

        // 3. 字母编号列表
        NumberingRenderData letterList = Numberings.ofLetter(
                "需求分析阶段",
                "设计开发阶段",
                "测试验证阶段",
                "上线部署阶段"
        ).create();
        data.put("letterList", letterList);

        InputStream templateStream = resourceLoader.getResource("classpath:templates/list_template.docx").getInputStream();
        XWPFTemplate template = XWPFTemplate.compile(templateStream).render(data);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        template.write(outputStream);
        template.close();
        templateStream.close();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "list_example.docx");

        return ResponseEntity.ok()
                .headers(headers)
                .body(outputStream.toByteArray());
    }

    /**
     * 图片示例 - 演示图片插入和调整
     */
    @GetMapping("/image-example")
    public ResponseEntity<byte[]> generateImageExample() throws IOException {

        Map<String, Object> data = new HashMap<>();

        // 1. 本地图片（如果存在）
        try {
            PictureRenderData localImage = Pictures.ofLocal("classpath:static/images/logo.png")
                    .size(150, 100)
                    .create();
            data.put("localImage", localImage);
        } catch (Exception e) {
            // 如果图片不存在，使用占位文本
            data.put("localImage", "本地图片未找到");
        }

        // 2. 网络图片示例（注意：实际使用时需要确保网络可访问）
        try {
            PictureRenderData webImage = Pictures.ofUrl("https://via.placeholder.com/200x150/4472C4/FFFFFF?text=POI-TL")
                    .size(200, 150)
                    .create();
            data.put("webImage", webImage);
        } catch (Exception e) {
            data.put("webImage", "网络图片加载失败");
        }

        // 3. Base64图片示例（1x1像素的透明PNG）
        String base64Data = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
        PictureRenderData base64Image = Pictures.ofBase64(base64Data, PictureType.PNG)
                .size(100, 100)
                .create();
        data.put("base64Image", base64Image);

        InputStream templateStream = resourceLoader.getResource("classpath:templates/image_template.docx").getInputStream();
        XWPFTemplate template = XWPFTemplate.compile(templateStream).render(data);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        template.write(outputStream);
        template.close();
        templateStream.close();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "image_example.docx");

        return ResponseEntity.ok()
                .headers(headers)
                .body(outputStream.toByteArray());
    }

    /**
     * 超链接示例 - 演示超链接文本
     */
    @GetMapping("/hyperlink-example")
    public ResponseEntity<byte[]> generateHyperlinkExample() throws IOException {

        Map<String, Object> data = new HashMap<>();

        // 1. 普通超链接
        HyperlinkTextRenderData link1 = Texts.of("访问POI-TL官网")
                .link("https://deepoove.com/poi-tl/")
                .color("0000FF")
                .underline()
                .create();
        data.put("officialLink", link1);

        // 2. 带样式的超链接
        HyperlinkTextRenderData link2 = Texts.of("Apache POI官方文档")
                .link("https://poi.apache.org/")
                .color("FF6600")
                .bold()
                .create();
        data.put("poiLink", link2);

        // 3. 邮箱链接
        HyperlinkTextRenderData emailLink = Texts.of("发送邮件")
                .link("mailto:<EMAIL>")
                .color("008000")
                .create();
        data.put("emailLink", emailLink);

        InputStream templateStream = resourceLoader.getResource("classpath:templates/hyperlink_template.docx").getInputStream();
        XWPFTemplate template = XWPFTemplate.compile(templateStream).render(data);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        template.write(outputStream);
        template.close();
        templateStream.close();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "hyperlink_example.docx");

        return ResponseEntity.ok()
                .headers(headers)
                .body(outputStream.toByteArray());
    }

    /**
     * 综合报告示例 - 演示实际业务场景的文档生成
     * 包含：标题、表格、图片、列表等多种元素
     */
    @GetMapping("/comprehensive-report")
    public ResponseEntity<byte[]> generateComprehensiveReport() throws IOException {

        Map<String, Object> data = new HashMap<>();

        // 1. 报告基本信息
        data.put("reportTitle", "月度销售报告");
        data.put("reportDate", "2024年1月");
        data.put("department", "销售部");
        data.put("reporter", "张经理");

        // 2. 执行摘要文本
        TextRenderData summaryTitle = Texts.of("执行摘要")
                .color("2F5597")
                .bold()
                .fontSize(16)
                .create();
        data.put("summaryTitle", summaryTitle);

        data.put("summaryContent", "本月销售业绩良好，总销售额达到150万元，同比增长15%。主要增长来源于新产品线的推广和老客户的复购。");

        // 3. 销售数据表格
        RowRenderData salesHeader = Rows.of("产品类别", "销售额(万元)", "同比增长", "占比")
                .textColor("FFFFFF")
                .bgColor("4472C4")
                .center()
                .bold()
                .create();

        RowRenderData sales1 = Rows.of("电子产品", "80", "+20%", "53.3%").center().create();
        RowRenderData sales2 = Rows.of("家居用品", "45", "+10%", "30.0%").center().create();
        RowRenderData sales3 = Rows.of("服装配饰", "25", "+5%", "16.7%").center().create();

        TableRenderData salesTable = Tables.of(salesHeader, sales1, sales2, sales3)
                .border(BorderStyle.DEFAULT)
                .create();
        data.put("salesTable", salesTable);

        // 4. 重点客户列表
        NumberingRenderData keyCustomers = Numberings.ofDecimal(
                "ABC科技有限公司 - 订单金额：35万元",
                "XYZ贸易公司 - 订单金额：28万元",
                "DEF制造企业 - 订单金额：22万元",
                "GHI零售连锁 - 订单金额：18万元"
        ).create();
        data.put("keyCustomers", keyCustomers);

        // 5. 下月计划
        NumberingRenderData nextMonthPlan = Numberings.create(
                "加强新产品推广力度",
                "深化与重点客户的合作关系",
                "开拓新的销售渠道",
                "提升客户服务质量"
        );
        data.put("nextMonthPlan", nextMonthPlan);

        // 6. 重要提醒
        TextRenderData importantNote = Texts.of("重要提醒：下月将推出春季促销活动，请各销售人员提前做好准备。")
                .color("FF0000")
                .bold()
                .create();
        data.put("importantNote", importantNote);

        InputStream templateStream = resourceLoader.getResource("classpath:templates/comprehensive_report_template.docx").getInputStream();
        XWPFTemplate template = XWPFTemplate.compile(templateStream).render(data);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        template.write(outputStream);
        template.close();
        templateStream.close();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "comprehensive_report.docx");

        return ResponseEntity.ok()
                .headers(headers)
                .body(outputStream.toByteArray());
    }

    /**
     * 动态表格示例 - 演示根据数据动态生成表格
     */
    @GetMapping("/dynamic-table")
    public ResponseEntity<byte[]> generateDynamicTable() throws IOException {

        Map<String, Object> data = new HashMap<>();

        // 模拟动态数据（实际应用中可能来自数据库）
        String[][] employeeData = {
                {"张三", "技术部", "Java工程师", "8000", "2年"},
                {"李四", "市场部", "销售经理", "7500", "3年"},
                {"王五", "人事部", "HR专员", "6500", "1年"},
                {"赵六", "财务部", "会计师", "7000", "4年"},
                {"钱七", "技术部", "前端工程师", "7800", "2年"}
        };

        // 创建表头
        RowRenderData header = Rows.of("姓名", "部门", "职位", "薪资", "工作年限")
                .textColor("FFFFFF")
                .bgColor("2F5597")
                .center()
                .bold()
                .create();

        // 动态创建数据行
        RowRenderData[] rows = new RowRenderData[employeeData.length + 1];
        rows[0] = header;

        for (int i = 0; i < employeeData.length; i++) {
            // 交替背景色
            if (i % 2 == 0) {
                rows[i + 1] = Rows.of(employeeData[i]).center().create();
            } else {
                rows[i + 1] = Rows.of(employeeData[i]).bgColor("F2F2F2").center().create();
            }
        }

        TableRenderData dynamicTable = Tables.of(rows).border(BorderStyle.DEFAULT).create();
        data.put("dynamicTable", dynamicTable);

        // 统计信息
        data.put("totalEmployees", String.valueOf(employeeData.length));
        data.put("avgSalary", "7360"); // 简单计算的平均薪资

        InputStream templateStream = resourceLoader.getResource("classpath:templates/dynamic_table_template.docx").getInputStream();
        XWPFTemplate template = XWPFTemplate.compile(templateStream).render(data);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        template.write(outputStream);
        template.close();
        templateStream.close();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "dynamic_table_example.docx");

        return ResponseEntity.ok()
                .headers(headers)
                .body(outputStream.toByteArray());
    }

    /**
     * 获取所有示例的说明文档
     */
    @GetMapping("/examples-info")
    public ResponseEntity<Map<String, Object>> getExamplesInfo() {

        Map<String, Object> info = new HashMap<>();
        info.put("title", "POI-TL常用示例说明");
        info.put("description", "基于Apache POI官方文档的实用示例集合");

        Map<String, Object> examples = new HashMap<>();

        examples.put("generate-word", "原始表格示例 - 演示合并单元格的复杂表格");
        examples.put("basic-text", "基础文本示例 - 演示文本替换和样式设置");
        examples.put("simple-table", "简单表格示例 - 演示基础表格创建");
        examples.put("complex-table", "复杂表格示例 - 演示合并单元格和复杂样式");
        examples.put("list-example", "列表示例 - 演示有序和无序列表");
        examples.put("image-example", "图片示例 - 演示图片插入和调整");
        examples.put("hyperlink-example", "超链接示例 - 演示超链接文本");
        examples.put("comprehensive-report", "综合报告示例 - 演示实际业务场景");
        examples.put("dynamic-table", "动态表格示例 - 演示根据数据动态生成表格");

        info.put("examples", examples);

        Map<String, String> usage = new HashMap<>();
        usage.put("访问方式", "GET请求各个示例接口");
        usage.put("返回格式", "Word文档文件下载");
        usage.put("模板位置", "src/main/resources/templates/");
        usage.put("官方文档", "https://deepoove.com/poi-tl/");

        info.put("usage", usage);

        return ResponseEntity.ok(info);
    }
}