# POI-TL 常用示例

基于Apache POI官方文档的poi-tl实用示例集合。

## 项目说明

本项目提供了poi-tl的常用功能示例，所有示例都直接在Controller中展示具体用法，便于学习和参考。

## 官方文档

- **poi-tl官方文档**: https://deepoove.com/poi-tl/
- **Apache POI官方文档**: https://poi.apache.org/
- **GitHub仓库**: https://github.com/Sayi/poi-tl

## 功能示例

### 1. 基础文本处理
- **接口**: `GET /basic-text`
- **功能**: 演示文本替换、样式设置（颜色、字体、大小、粗体、斜体等）
- **模板**: `basic_text_template.docx`

### 2. 简单表格
- **接口**: `GET /simple-table`
- **功能**: 演示基础表格创建、表头样式、数据行格式
- **模板**: `simple_table_template.docx`

### 3. 复杂表格
- **接口**: `GET /complex-table`
- **功能**: 演示合并单元格、多层表头、复杂样式
- **模板**: `complex_table_template.docx`

### 4. 原始表格示例
- **接口**: `GET /generate-word`
- **功能**: 原有的表格示例，演示合并单元格
- **模板**: `template.docx`

### 5. 列表功能
- **接口**: `GET /list-example`
- **功能**: 演示有序列表、无序列表、字母编号列表
- **模板**: `list_template.docx`

### 6. 图片处理
- **接口**: `GET /image-example`
- **功能**: 演示本地图片、网络图片、Base64图片插入
- **模板**: `image_template.docx`

### 7. 超链接
- **接口**: `GET /hyperlink-example`
- **功能**: 演示网页链接、邮箱链接、带样式的超链接
- **模板**: `hyperlink_template.docx`

### 8. 综合报告
- **接口**: `GET /comprehensive-report`
- **功能**: 演示实际业务场景，包含多种元素的综合文档
- **模板**: `comprehensive_report_template.docx`

### 9. 动态表格
- **接口**: `GET /dynamic-table`
- **功能**: 演示根据数据动态生成表格，交替背景色
- **模板**: `dynamic_table_template.docx`

### 10. 示例说明
- **接口**: `GET /examples-info`
- **功能**: 返回所有示例的说明文档（JSON格式）

## 快速开始

### 1. 启动应用
```bash
mvn spring-boot:run
```

### 2. 访问示例
```bash
# 基础文本示例
curl -O -J "http://localhost:8080/basic-text"

# 简单表格示例
curl -O -J "http://localhost:8080/simple-table"

# 复杂表格示例
curl -O -J "http://localhost:8080/complex-table"

# 获取所有示例说明
curl "http://localhost:8080/examples-info"
```

### 3. 浏览器访问
直接在浏览器中访问以下URL即可下载对应的Word文档：
- http://localhost:8080/basic-text
- http://localhost:8080/simple-table
- http://localhost:8080/complex-table
- http://localhost:8080/list-example
- http://localhost:8080/image-example
- http://localhost:8080/hyperlink-example
- http://localhost:8080/comprehensive-report
- http://localhost:8080/dynamic-table

## 模板文件

模板文件位于 `src/main/resources/templates/` 目录下，包含以下文件：

1. `template.docx` - 原始表格示例模板
2. `basic_text_template.docx` - 基础文本模板
3. `simple_table_template.docx` - 简单表格模板
4. `complex_table_template.docx` - 复杂表格模板
5. `list_template.docx` - 列表模板
6. `image_template.docx` - 图片模板
7. `hyperlink_template.docx` - 超链接模板
8. `comprehensive_report_template.docx` - 综合报告模板
9. `dynamic_table_template.docx` - 动态表格模板

## 代码特点

1. **直观易懂**: 所有示例都直接在Controller中展示，无过度封装
2. **即用即学**: 每个示例都包含详细的注释说明
3. **官方标准**: 基于Apache POI官方文档的最佳实践
4. **实用性强**: 涵盖实际开发中的常用场景

## 主要依赖

```xml
<dependency>
    <groupId>com.deepoove</groupId>
    <artifactId>poi-tl</artifactId>
</dependency>
<dependency>
    <groupId>com.deepoove</groupId>
    <artifactId>poi-ooxml-schemas-extra</artifactId>
</dependency>
```

## 注意事项

1. 确保模板文件存在于 `src/main/resources/templates/` 目录
2. 图片示例需要相应的图片文件或网络访问权限
3. 所有示例都返回Word文档的字节流，可直接下载

## 扩展开发

基于现有示例，您可以：

1. 修改模板文件来调整文档样式
2. 在Controller中添加新的示例方法
3. 结合数据库查询生成动态内容
4. 添加更多的POI-TL高级功能

## 技术支持

- 查看poi-tl官方文档获取更多功能说明
- 参考Apache POI官方文档了解底层实现
- 查看项目源码了解具体实现细节